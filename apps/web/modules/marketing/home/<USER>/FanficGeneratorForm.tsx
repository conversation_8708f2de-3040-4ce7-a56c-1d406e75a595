"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import {
	EditIcon,
	LoaderIcon,
	RefreshCwIcon,
	SaveIcon,
	TrashIcon,
	WandIcon,
	XIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";

interface StorySettings {
	fandom: string;
	genre: string;
	mainCharacters: string;
	tone: string;
	contentRating: string;
}

interface StoryParagraph {
	id: string;
	content: string;
	isEditing: boolean;
}

type GenerationMode = "setup" | "writing";

export function FanficGeneratorForm() {
	const t = useTranslations("aiFanficGenerator.form");
	const tResult = useTranslations("aiFanficGenerator.result");

	// State management
	const [mode, setMode] = useState<GenerationMode>("setup");
	const [storySettings, setStorySettings] = useState<StorySettings>({
		fandom: "",
		genre: "",
		mainCharacters: "",
		tone: "",
		contentRating: "",
	});
	const [paragraphs, setParagraphs] = useState<StoryParagraph[]>([]);
	const [currentPrompt, setCurrentPrompt] = useState("");
	const [isGenerating, setIsGenerating] = useState(false);
	const [isGeneratingSuggestion, setIsGeneratingSuggestion] = useState(false);
	const [editingParagraph, setEditingParagraph] = useState<string | null>(
		null,
	);
	const [editContent, setEditContent] = useState("");

	// Refs
	const storyContainerRef = useRef<HTMLDivElement>(null);

	// Predefined options
	const genreOptions = [
		"Romance",
		"Adventure",
		"Mystery",
		"Drama",
		"Comedy",
		"Horror",
		"Fantasy",
		"Sci-Fi",
		"Slice of Life",
		"Angst",
		"Fluff",
		"Hurt/Comfort",
	];

	const toneOptions = [
		"Romantic",
		"Humorous",
		"Dramatic",
		"Dark",
		"Light-hearted",
		"Mysterious",
		"Adventurous",
		"Emotional",
		"Playful",
	];

	const contentRatingOptions = [
		{ value: "general", label: "General (All Ages)" },
		{ value: "teen", label: "Teen (13+)" },
		{ value: "mature", label: "Mature (18+)" },
	];

	// Auto-scroll to bottom when new paragraph is added
	useEffect(() => {
		if (storyContainerRef.current) {
			storyContainerRef.current.scrollTop =
				storyContainerRef.current.scrollHeight;
		}
	}, [paragraphs]);

	const handleSettingsChange = (
		field: keyof StorySettings,
		value: string,
	) => {
		setStorySettings((prev) => ({ ...prev, [field]: value }));
	};

	const startWriting = () => {
		if (!storySettings.fandom || !storySettings.mainCharacters) {
			alert(t("errors.missingRequired"));
			return;
		}
		setMode("writing");
	};

	const generateParagraph = async () => {
		if (!currentPrompt.trim() && paragraphs.length === 0) {
			alert(t("errors.needGuidance"));
			return;
		}

		setIsGenerating(true);
		try {
			const response = await fetch("/api/generate-paragraph", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					storySettings,
					existingParagraphs: paragraphs.map((p) => p.content),
					prompt: currentPrompt.trim(),
					isFirstParagraph: paragraphs.length === 0,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				const errorMessage =
					errorData.error ||
					`HTTP ${response.status}: ${response.statusText}`;
				console.error("API Error:", errorMessage);
				throw new Error(errorMessage);
			}

			const data = await response.json();
			const newParagraph: StoryParagraph = {
				id: Date.now().toString(),
				content: data.paragraph,
				isEditing: false,
			};

			setParagraphs((prev) => [...prev, newParagraph]);
			setCurrentPrompt("");
		} catch (error) {
			console.error("Error generating paragraph:", error);
			const errorMessage =
				error instanceof Error
					? error.message
					: t("errors.unknownError");
			alert(`${t("errors.generateError")}: ${errorMessage}`);
		} finally {
			setIsGenerating(false);
		}
	};

	const regenerateLastParagraph = async () => {
		if (paragraphs.length === 0) return;

		const lastParagraph = paragraphs[paragraphs.length - 1];
		setIsGenerating(true);

		try {
			const response = await fetch("/api/generate-paragraph", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					storySettings,
					existingParagraphs: paragraphs
						.slice(0, -1)
						.map((p) => p.content),
					prompt: t("prompts.continueStory"),
					isFirstParagraph: paragraphs.length === 1,
				}),
			});

			if (!response.ok) {
				throw new Error(t("errors.regenerateFailed"));
			}

			const data = await response.json();
			setParagraphs((prev) =>
				prev.map((p, index) =>
					index === prev.length - 1
						? { ...p, content: data.paragraph }
						: p,
				),
			);
		} catch (error) {
			console.error("Error regenerating paragraph:", error);
			alert(t("errors.regenerateError"));
		} finally {
			setIsGenerating(false);
		}
	};

	const deleteParagraph = (id: string) => {
		setParagraphs((prev) => prev.filter((p) => p.id !== id));
	};

	const startEditParagraph = (id: string, content: string) => {
		setEditingParagraph(id);
		setEditContent(content);
	};

	const saveEditParagraph = () => {
		if (!editingParagraph) return;

		setParagraphs((prev) =>
			prev.map((p) =>
				p.id === editingParagraph ? { ...p, content: editContent } : p,
			),
		);
		setEditingParagraph(null);
		setEditContent("");
	};

	const cancelEditParagraph = () => {
		setEditingParagraph(null);
		setEditContent("");
	};

	const saveStory = () => {
		const storyData = {
			settings: storySettings,
			paragraphs: paragraphs.map((p) => p.content),
			timestamp: new Date().toISOString(),
		};

		const blob = new Blob([JSON.stringify(storyData, null, 2)], {
			type: "application/json",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `fanfic-${storySettings.fandom || "story"}-${Date.now()}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const generateSuggestion = async () => {
		setIsGeneratingSuggestion(true);
		try {
			const response = await fetch("/api/generate-suggestion", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					storySettings,
					existingParagraphs: paragraphs.map((p) => p.content),
					isFirstParagraph: paragraphs.length === 0,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				const errorMessage =
					errorData.error ||
					`HTTP ${response.status}: ${response.statusText}`;
				console.error("API Error:", errorMessage);
				throw new Error(errorMessage);
			}

			const data = await response.json();
			setCurrentPrompt(data.suggestion);
		} catch (error) {
			console.error("Error generating suggestion:", error);
			const errorMessage =
				error instanceof Error
					? error.message
					: "Unknown error occurred";
			alert(`Error generating suggestion: ${errorMessage}`);
		} finally {
			setIsGeneratingSuggestion(false);
		}
	};

	const copyStory = () => {
		const fullStory = paragraphs.map((p) => p.content).join("\n\n");
		navigator.clipboard.writeText(fullStory);
		alert("Story copied to clipboard!");
	};

	if (mode === "setup") {
		return (
			<div className="mx-auto max-w-4xl">
				<div className="mb-8 text-center">
					<h2 className="mb-2 font-bold text-3xl lg:text-4xl">
						{t("title")}
					</h2>
					<p className="text-foreground/60 text-lg">
						{t("subtitle")}
					</p>
				</div>
				<div className="rounded-lg border bg-card p-8">
					<h3 className="mb-4 font-semibold text-xl">Story Setup</h3>
					<div className="space-y-6">
						<div className="grid gap-4 md:grid-cols-2">
							<div>
								<label className="mb-2 block font-medium text-sm">
									{t("questions.fandom.label")} *
								</label>
								<Input
									value={storySettings.fandom}
									onChange={(e) =>
										handleSettingsChange(
											"fandom",
											e.target.value,
										)
									}
									placeholder={t(
										"questions.fandom.placeholder",
									)}
									className="w-full"
									required
								/>
							</div>
							<div>
								<label className="mb-2 block font-medium text-sm">
									{t("questions.mainCharacters.label")} *
								</label>
								<Input
									value={storySettings.mainCharacters}
									onChange={(e) =>
										handleSettingsChange(
											"mainCharacters",
											e.target.value,
										)
									}
									placeholder={t(
										"questions.mainCharacters.placeholder",
									)}
									className="w-full"
									required
								/>
							</div>
						</div>

						<div className="grid gap-4 md:grid-cols-3">
							<div>
								<label className="mb-2 block font-medium text-sm">
									{t("questions.genre.label")}
								</label>
								<Select
									value={storySettings.genre}
									onValueChange={(value) =>
										handleSettingsChange("genre", value)
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select genre" />
									</SelectTrigger>
									<SelectContent>
										{genreOptions.map((genre) => (
											<SelectItem
												key={genre}
												value={genre}
											>
												{genre}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<label className="mb-2 block font-medium text-sm">
									{t("questions.tone.label")}
								</label>
								<Select
									value={storySettings.tone}
									onValueChange={(value) =>
										handleSettingsChange("tone", value)
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select tone" />
									</SelectTrigger>
									<SelectContent>
										{toneOptions.map((tone) => (
											<SelectItem key={tone} value={tone}>
												{tone}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							<div>
								<label className="mb-2 block font-medium text-sm">
									{t("questions.contentRating.label")}
								</label>
								<Select
									value={storySettings.contentRating}
									onValueChange={(value) =>
										handleSettingsChange(
											"contentRating",
											value,
										)
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select rating" />
									</SelectTrigger>
									<SelectContent>
										{contentRatingOptions.map((rating) => (
											<SelectItem
												key={rating.value}
												value={rating.value}
											>
												{rating.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
					</div>
				</div>

				<div className="flex justify-center mt-12">
					<Button
						onClick={startWriting}
						size="lg"
						disabled={
							!storySettings.fandom ||
							!storySettings.mainCharacters
						}
						className="min-w-48"
					>
						Start Writing Story
					</Button>
				</div>
			</div>
		);
	}

	// Writing mode
	return (
		<div className="mx-auto max-w-4xl">
			<div className="mb-6 flex items-center justify-between">
				<div>
					<h2 className="font-bold text-2xl">AI Fanfic Generator</h2>
					<p className="text-foreground/60 text-sm">
						{storySettings.fandom} • {storySettings.mainCharacters}
					</p>
				</div>
				<Button
					variant="outline"
					onClick={() => setMode("setup")}
					size="sm"
				>
					Back to Setup
				</Button>
			</div>

			{/* Story Container */}
			<div
				ref={storyContainerRef}
				className="mb-6 max-h-96 overflow-y-auto rounded-lg border bg-card p-4"
			>
				{paragraphs.length === 0 ? (
					<p className="text-center text-foreground/60 text-sm">
						The first paragraph is crucial. Please review and edit
						it carefully, as the rest of the story's style will be
						based on it.
						<br /> Start by giving some guidance below.
					</p>
				) : (
					<div className="space-y-4">
						{paragraphs.map((paragraph, index) => (
							<div
								key={paragraph.id}
								className="group relative rounded-lg border bg-card p-4 hover:shadow-sm transition-shadow"
							>
								{editingParagraph === paragraph.id ? (
									<div className="space-y-2">
										<Textarea
											value={editContent}
											onChange={(e) =>
												setEditContent(e.target.value)
											}
											className="min-h-24 w-full"
										/>
										<div className="flex gap-2">
											<Button
												size="sm"
												onClick={saveEditParagraph}
											>
												<SaveIcon className="mr-1 size-3" />
												Save
											</Button>
											<Button
												size="sm"
												variant="outline"
												onClick={cancelEditParagraph}
											>
												<XIcon className="mr-1 size-3" />
												Cancel
											</Button>
										</div>
									</div>
								) : (
									<div className="space-y-3">
										<div className="whitespace-pre-wrap text-sm leading-relaxed pr-20">
											{paragraph.content}
										</div>
										<div className="flex justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
											<Button
												size="sm"
												variant="ghost"
												onClick={() =>
													startEditParagraph(
														paragraph.id,
														paragraph.content,
													)
												}
												className="h-8 px-2"
											>
												<EditIcon className="size-3" />
											</Button>
											<Button
												size="sm"
												variant="ghost"
												onClick={() =>
													deleteParagraph(
														paragraph.id,
													)
												}
												className="h-8 px-2"
											>
												<TrashIcon className="size-3" />
											</Button>
											{index ===
												paragraphs.length - 1 && (
												<Button
													size="sm"
													variant="ghost"
													onClick={
														regenerateLastParagraph
													}
													disabled={isGenerating}
													className="h-8 px-2"
												>
													<RefreshCwIcon className="size-3" />
												</Button>
											)}
										</div>
									</div>
								)}
							</div>
						))}
					</div>
				)}
			</div>

			{/* Story Controls */}
			<div className="space-y-4">
				<div>
					<div className="mb-2 flex items-center justify-between">
						<label
							htmlFor="story-prompt"
							className="block font-medium text-sm"
						>
							What should happen next? (optional)
						</label>
						<Button
							variant="outline"
							size="sm"
							onClick={generateSuggestion}
							disabled={isGeneratingSuggestion}
							className="flex items-center gap-2"
						>
							{isGeneratingSuggestion ? (
								<>
									<LoaderIcon className="size-3 animate-spin" />
									Generating...
								</>
							) : (
								<>
									<WandIcon className="size-3" />
									AI Suggest
								</>
							)}
						</Button>
					</div>
					<Textarea
						id="story-prompt"
						value={currentPrompt}
						onChange={(e) => setCurrentPrompt(e.target.value)}
						placeholder="Describe what you want to happen next in the story..."
						className="min-h-20 w-full"
					/>
				</div>

				<div className="flex flex-wrap gap-3">
					<Button
						onClick={generateParagraph}
						disabled={isGenerating}
						className="flex-1 min-w-32"
					>
						{isGenerating ? (
							<>
								<LoaderIcon className="mr-2 size-4 animate-spin" />
								Generating...
							</>
						) : (
							"Next Paragraph"
						)}
					</Button>

					{paragraphs.length > 0 && (
						<>
							<Button variant="outline" onClick={copyStory}>
								Copy Story
							</Button>
							<Button variant="outline" onClick={saveStory}>
								Save Story
							</Button>
						</>
					)}
				</div>
			</div>
		</div>
	);
}
