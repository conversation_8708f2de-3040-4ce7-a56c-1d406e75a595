import { GoogleGenAI } from "@google/genai";
import { type NextRequest, NextResponse } from "next/server";

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

export async function POST(request: NextRequest) {
	try {
		if (!GEMINI_API_KEY) {
			console.error("Gemini API key is not configured");
			return NextResponse.json(
				{ error: "AI service is not properly configured" },
				{ status: 500 },
			);
		}

		const body = await request.json();
		const { storySettings, existingParagraphs, prompt, isFirstParagraph } =
			body;

		// Validate required fields
		if (
			!storySettings ||
			!storySettings.fandom ||
			!storySettings.mainCharacters
		) {
			return NextResponse.json(
				{
					error: "Story settings with fandom and main characters are required",
				},
				{ status: 400 },
			);
		}

		// Build context from story settings
		let contextPrompt = `You are writing a fanfiction story with the following settings:
- Fandom/Universe: ${storySettings.fandom}
- Main Characters: ${storySettings.mainCharacters}`;

		if (storySettings.genre) {
			contextPrompt += `\n- Genre: ${storySettings.genre}`;
		}
		if (storySettings.tone) {
			contextPrompt += `\n- Tone: ${storySettings.tone}`;
		}
		if (storySettings.contentRating) {
			contextPrompt += `\n- Content Rating: ${storySettings.contentRating}`;
		}

		// Add existing story context
		if (existingParagraphs && existingParagraphs.length > 0) {
			contextPrompt += `\n\nExisting story so far:\n${existingParagraphs.join("\n\n")}`;
		}

		// Create the specific prompt for this paragraph
		let paragraphPrompt;

		if (isFirstParagraph) {
			paragraphPrompt = `${contextPrompt}

Write the opening paragraph of this fanfiction story. ${prompt ? `User guidance: ${prompt}` : ""}

Requirements:
1. Create an engaging opening that establishes the setting and introduces the main character(s)
2. Stay true to the fandom and characters
3. Match the specified tone and genre
4. Propel the plot forward, avoiding stagnation.
5. Write in a compelling, descriptive style
6. End the paragraph in a way that naturally leads to the next part of the story

Write the paragraph directly without any additional commentary or formatting.`;
		} else {
			paragraphPrompt = `${contextPrompt}

Continue the story by writing the next paragraph. ${prompt ? `User guidance for this paragraph: ${prompt}` : "Continue the story naturally based on what has happened so far."}

Requirements:
1. Maintain Character Consistency: Ensure characters act and speak in a way that is true to their established personalities.
2. Continue Story Style and Tone: The new text should seamlessly match the existing narrative's voice and mood.
3. Advance the Plot: Move the story forward and avoid staying in one place.
4. Use Vivid, Descriptive Language: Paint a clear and engaging picture for the reader.
5. Avoid Redundancy: Do not repeat information or events that have already been mentioned.

Write the paragraph directly without any additional commentary or formatting.`;
		}

		// Initialize Google GenAI
		const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

		// Call Gemini API
		const response = await ai.models.generateContent({
			model: "gemini-2.5-flash",
			contents: [
				{
					role: "user",
					parts: [{ text: paragraphPrompt }],
				},
			],
		});

		const paragraph = response.text?.trim() || "";

		return NextResponse.json({ paragraph });
	} catch (error) {
		console.error("Error generating paragraph:", error);

		// Provide more specific error messages
		let errorMessage = "Error generating paragraph";

		if (error instanceof Error) {
			if (error.message.includes("API key")) {
				errorMessage =
					"API key is not configured. Please contact the administrator.";
			} else if (error.message.includes("fetch failed")) {
				errorMessage =
					"Network error. Please check your internet connection and try again.";
			} else if (error.message.includes("quota")) {
				errorMessage = "API limit reached. Please try again later.";
			} else {
				errorMessage = `Error: ${error.message}`;
			}
		}

		return NextResponse.json({ error: errorMessage }, { status: 500 });
	}
}
