import { GoogleGenAI } from "@google/genai";
import { type NextRequest, NextResponse } from "next/server";

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

export async function POST(request: NextRequest) {
	try {
		if (!GEMINI_API_KEY) {
			console.error("Gemini API key is not configured");
			return NextResponse.json(
				{ error: "AI service is not properly configured" },
				{ status: 500 },
			);
		}

		const body = await request.json();
		const { storySettings, existingParagraphs } = body;

		// Validate required fields
		if (
			!storySettings ||
			!storySettings.fandom ||
			!storySettings.mainCharacters
		) {
			return NextResponse.json(
				{
					error: "Story settings with fandom and main characters are required",
				},
				{ status: 400 },
			);
		}

		// Build context from story settings
		let contextPrompt = `You are analyzing a fanfiction story with the following settings:
- Fandom/Universe: ${storySettings.fandom}
- Main Characters: ${storySettings.mainCharacters}`;

		if (storySettings.genre) {
			contextPrompt += `\n- Genre: ${storySettings.genre}`;
		}
		if (storySettings.tone) {
			contextPrompt += `\n- Tone: ${storySettings.tone}`;
		}
		if (storySettings.contentRating) {
			contextPrompt += `\n- Content Rating: ${storySettings.contentRating}`;
		}

		// Add existing story context
		if (existingParagraphs && existingParagraphs.length > 0) {
			contextPrompt += `\n\nExisting story so far:\n${existingParagraphs.join("\n\n")}`;
		}

		// Create the specific prompt for generating suggestions
		const suggestionPrompt = `${contextPrompt}

Based on the story context above, generate a brief suggestion (2-3 sentences) for what could happen next in the story. This should be a plot direction or story development that would naturally advance the narrative.

Requirements:
1. Provide a clear direction that can advance the plot
2. Consider the existing story content and build upon it naturally
3. Keep it concise (2-3 sentences maximum)
4. Make it engaging and interesting
5. Stay true to the fandom, characters, genre, and tone

Write only the suggestion without any additional commentary, formatting, or explanations.`;

		// Initialize Google GenAI
		const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

		// Call Gemini API
		const response = await ai.models.generateContent({
			model: "gemini-2.5-flash",
			contents: [
				{
					role: "user",
					parts: [{ text: suggestionPrompt }],
				},
			],
		});

		const suggestion = response.text?.trim() || "";

		return NextResponse.json({ suggestion });
	} catch (error) {
		console.error("Error generating suggestion:", error);

		// Provide more specific error messages
		let errorMessage = "Error generating suggestion";

		if (error instanceof Error) {
			if (error.message.includes("API key")) {
				errorMessage =
					"API key is not configured. Please contact the administrator.";
			} else if (error.message.includes("fetch failed")) {
				errorMessage =
					"Network error. Please check your internet connection and try again.";
			} else if (error.message.includes("quota")) {
				errorMessage = "API limit reached. Please try again later.";
			} else {
				errorMessage = `Error: ${error.message}`;
			}
		}

		return NextResponse.json({ error: errorMessage }, { status: 500 });
	}
}
